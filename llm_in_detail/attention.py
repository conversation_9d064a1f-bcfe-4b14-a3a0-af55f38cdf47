import torch
from torch import nn
from loguru import logger
import math
import numpy as np

import torch.nn.functional as F

import pytest


def simple_attention(q, k, v):
    scale = 1 / np.sqrt(q.shape[-1])
    q = q * scale

    scores = torch.matmul(q, k.transpose(-2, -1))

    logger.debug(f"scores: {scores.shape}")

    scores = torch.softmax(scores, dim=-1)
    output = torch.matmul(scores, v)

    logger.debug(f"scores: {scores.shape}")

    output = torch.matmul(scores, v)

    return output


# for mha, single_batch
def flash_attn1(
    q, k, v, dropout_p=0.0, softmax_scale=None, debug=False, tile_row=2, tile_col=3
):
    qlen, klen = q.shape[-2], k.shape[-2]
    hidden_size = q.shape[-1]
    scale = 1 / np.sqrt(q.shape[-1])
    q = q * scale
    o = torch.zeros_like(q)
    l = torch.zeros(qlen, dtype=torch.float32)
    m = torch.ones(qlen, dtype=torch.float32) * -torch.inf

    for i in range(0, klen, tile_col):
        k_tile = k[i : i + tile_col, :]
        v_tile = v[i : i + tile_col, :]
        for j in range(0, qlen, tile_row):
            q_tile = q[j : j + tile_row, :]
            o_tile = o[j : j + tile_row, :]  # Fixed: should be o, not q
            s_tile = torch.matmul(q_tile, k_tile.transpose(-2, -1))
            m_tile = torch.max(s_tile, dim=-1, keepdim=False).values

            p_tile = torch.exp(s_tile - m_tile.unsqueeze(-1))
            l_tile = torch.sum(p_tile, dim=-1, keepdim=False)

            m_new = torch.maximum(m_tile, m[j : j + tile_row])

            l_new = (
                torch.exp(m[j : j + tile_row] - m_new) * l[j : j + tile_row]
                + torch.exp(m_tile - m_new) * l_tile
            )

            # Compute the updated output using the correct Flash Attention formula
            # t1: contribution from previous output
            alpha = torch.exp(m[j : j + tile_row] - m_new) * l[j : j + tile_row] / l_new
            t1 = alpha.unsqueeze(-1) * o_tile

            # t2: contribution from current tile
            beta = torch.exp(m_tile - m_new) / l_new
            t2 = beta.unsqueeze(-1) * (p_tile @ v_tile)

            # if debug:
            #     logger.debug(f"p_tile: {p_tile}")
            #     logger.debug(f"l_tile: {l_tile}")
            #     logger.debug(f"m_new: {m_new}")
            #     logger.debug(f"l_new: {l_new}")
            #     logger.debug(f"o_tile: {o_tile}")
            #     logger.debug(f"alpha: {alpha}")
            #     logger.debug(f"beta: {beta}")
            #     logger.debug(f"t1: {t1}")
            #     logger.debug(f"t2: {t2}")

            o[j : j + tile_row, :] = t1 + t2
            l[j : j + tile_row] = l_new
            m[j : j + tile_row] = m_new

    if debug:
        print(f"{l=}")
        print(f"{m=}")
    return o

# for mha, single_batch
def flash_attn2(
    q, k, v, dropout_p=0.0, softmax_scale=None, debug=False, tile_row=2, tile_col=3
):
    qlen, klen = q.shape[-2], k.shape[-2]
    hidden_size = q.shape[-1]
    scale = 1 / np.sqrt(q.shape[-1])
    q = q * scale
    o = torch.zeros_like(q)
    l = torch.zeros(qlen, dtype=torch.float32)
    m = torch.ones(qlen, dtype=torch.float32) * -torch.inf


    for j in range(0, qlen, tile_row):
        q_tile = q[j : j + tile_row, :]
        o_tile = o[j : j + tile_row, :]
        l_tile = torch.zeros(tile_row, dtype=torch.float32) # 外提

        for i in range(0, klen, tile_col):
            k_tile = k[i : i + tile_col, :]
            v_tile = v[i : i + tile_col, :]
            s_tile = torch.matmul(q_tile, k_tile.transpose(-2, -1))
            m_tile = torch.max(s_tile, dim=-1, keepdim=False).values
            

            p_tile = torch.exp(s_tile - m_tile.unsqueeze(-1))
            l_tmp = torch.sum(p_tile, dim=-1, keepdim=False)

            m_new = torch.maximum(m_tile, m[j : j + tile_row])

            l_tile = (
                torch.exp(m[j : j + tile_row] - m_new) * l_tile
                + torch.exp(m_tile - m_new) * l_tmp
            )

            # t1: contribution from previous output
            alpha = torch.exp(m[j : j + tile_row] - m_new) * l[j : j + tile_row] # / l_new
            t1 = alpha.unsqueeze(-1) * o_tile

            # t2: contribution from current tile
            beta = torch.exp(m_tile - m_new) # / l_new
            t2 = beta.unsqueeze(-1) * (p_tile @ v_tile)

            if debug:
                logger.debug(f"p_tile: {p_tile}")
                logger.debug(f"l_tile: {l_tile}")
                logger.debug(f"m_new: {m_new}")
                # logger.debug(f"l_new: {l_new}")
                logger.debug(f"o_tile: {o_tile}")
                logger.debug(f"alpha: {alpha}")
                logger.debug(f"beta: {beta}")
                logger.debug(f"t1: {t1}")
                logger.debug(f"t2: {t2}")

        o[j : j + tile_row, :] = torch.diag(1/l_tile) * o_tile 
        l[j : j + tile_row] = l_tile
        m[j : j + tile_row] = m_new

    if debug:
        print(f"{l=}")
        print(f"{m=}")
    return o

@pytest.mark.parametrize(
    "q_len, seq_len, hidden_size, tile_row, tile_col",
    [
        (1, 3, 8, 2, 3),
        (3, 4, 64, 3, 2),
        (8, 8, 64, 2, 4),
        (2, 128, 64, 2, 4),
        (8, 128, 64, 4, 4),
    ],
)
def test_flash_attn(q_len, seq_len, hidden_size, tile_row, tile_col):
    torch.manual_seed(0)

    q = torch.randn(q_len, hidden_size)
    k = torch.randn(seq_len, hidden_size)
    v = torch.randn(seq_len, hidden_size)

    o1 = flash_attn1(q, k, v, tile_row=tile_row, tile_col=tile_col, debug=True)
    o2 = flash_attn2(q, k, v, tile_row=tile_row, tile_col=tile_col, debug=True)

    golden = simple_attention(q, k, v)

    print(f"{golden.shape=}")

    print(f"{o1.shape=}")
    print(f"{o2.shape=}")
    print(f"{o1=}")
    print(f"{o2=}")
    print(f"{golden=}")
    # print(f"{torch.max(torch.abs(o1-golden))=}")

    assert torch.allclose(o1, golden, atol=1e-6)
    assert torch.allclose(o2, golden, atol=1e-6)


if __name__ == "__main__":
    print("---------0-------")
    test_flash_attn(2, 2, 2, 2, 2)
    print("---------11-------")
    test_flash_attn(3, 2, 2, 3, 2)
    print("---------12-------")
    # test_flash_attn(4, 2, 2, 2, 2)
    # print("---------13-------")
    # test_flash_attn(2, 4, 2, 2, 2)
    # print("---------14-------")
    # test_flash_attn(3, 4, 2, 3, 2)
    # print("---------2-------")
    # test_flash_attn(3, 4, 2, 3, 2)
    # print("---------3-------")
    # test_flash_attn(8, 8, 64, 2, 4)